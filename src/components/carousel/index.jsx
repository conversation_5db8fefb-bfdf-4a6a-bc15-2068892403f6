// "use client";

// import React, { useState, useEffect, TouchEvent } from "react";
// import { motion, AnimatePresence } from "framer-motion";
// import { useRouter } from "next/navigation";

// const TOYS = [
//   {
//     id: 1,
//     image: "/images/hero/happiness.png",
//     name: "Happiness",
//     description: "The Monster Labubu Big Into Energy Vinyl Plush Pendant.",
//     price: 599,
//   },
//   {
//     id: 2,
//     image: "/images/hero/hope.png",
//     name: "Hope",
//     description: "The Monster Labubu Big Into Energy Vinyl Plush Pendant.",
//     price: 599,
//   },
//   {
//     id: 3,
//     image: "/images/hero/love.png",
//     name: "Love",
//     description: "The Monster Labubu Big Into Energy Vinyl Plush Pendant.",
//     price: 599,
//   },
// ];

// const PlushToysCarousel = () => {
//   const [activeIndex, setActiveIndex] = useState(0);
//   const router = useRouter();

//   // Auto-rotate toys
//   useEffect(() => {
//     const interval = setInterval(() => {
//       setActiveIndex((prev) => (prev + 1) % TOYS.length);
//     }, 4000);

//     return () => clearInterval(interval);
//   }, []);

//   const activeToy = TOYS[activeIndex];

//   const handleTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
//     const touchStartX = e.touches[0].clientX;

//     const handleTouchEnd = (endEvent: globalThis.TouchEvent) => {
//       const touchEndX = endEvent.changedTouches[0].clientX;
//       const diff = touchStartX - touchEndX;

//       if (Math.abs(diff) > 50) {
//         // Minimum swipe distance
//         if (diff > 0) {
//           // Swiped left, go to next
//           setActiveIndex((prev) => (prev + 1) % TOYS.length);
//         } else {
//           // Swiped right, go to previous
//           setActiveIndex((prev) => (prev - 1 + TOYS.length) % TOYS.length);
//         }
//       }

//       // Remove the event listener
//       document.removeEventListener("touchend", handleTouchEnd);
//     };

//     // Add the event listener for touchend
//     document.addEventListener("touchend", handleTouchEnd, { once: true });
//   };

//   return (
//     <div className="relative w-full min-h-[500px] bg-custom-gradient overflow-hidden">
//       {/* Cloud decorations */}
//       <div className="absolute bottom-0 left-0 w-full">
//         <svg
//           viewBox="0 0 1440 100"
//           fill="white"
//           xmlns="http://www.w3.org/2000/svg"
//         >
//           <path d="M0,70 C150,110 350,30 500,70 C650,110 800,30 950,70 C1100,110 1300,30 1440,70 L1440,100 L0,100 Z" />
//         </svg>
//       </div>

//       <div className="container mx-auto px-4 py-8 md:py-16 flex flex-col md:flex-row items-center justify-between relative z-10">
//         {/* Left column - Text content */}
//         <div className="w-full md:w-1/2 mb-6 md:mb-0 px-4 text-center md:text-left">
//           <p className="uppercase text-sm font-medium tracking-wide text-gray-700">
//             EARLY BIRD SALE - UP TO 25% OFF
//           </p>

//           <AnimatePresence mode="wait">
//              <motion.div
//               key={activeToy.id}
//               initial={{ opacity: 0, y: 20 }}
//               animate={{ opacity: 1, y: 0 }}
//               exit={{ opacity: 0, y: -20 }}
//               transition={{ duration: 0.5 }}
//             >
//               <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mt-2 mb-2">
//                 {activeToy.name}
//               </h1>
//               <p className="text-base md:text-lg text-gray-600 mb-4">
//                 {activeToy.description}
//               </p>
//               <p className="text-base md:text-lg text-gray-700 mb-6">
//                 Price from : Rs.{activeToy.price}
//               </p>
//             </motion.div>
//           </AnimatePresence>

//           <button
//             onClick={() => router.push("/shop/product/labubu-plush-keychain")}
//             className="button_slide bg-white text-primary py-2 px-6 md:py-3 md:px-8 rounded-full font-medium transition-colors duration-300"
//           >
//             Shop Now
//           </button>
//         </div>

//         {/* Right column - Carousel */}
//         <div className="w-full md:w-1/2 h-[350px] sm:h-[380px] md:h-[350px] lg:h-[400px] relative">
//           {/* Mobile carousel with visible previous and next items */}
//           <div className="md:hidden w-full h-full relative overflow-visible">
//             <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full">
//               {TOYS.map((toy, index) => {
//                 // Calculate position relative to active index
//                 const isActive = index === activeIndex;
//                 const isPrev =
//                   index === (activeIndex - 1 + TOYS.length) % TOYS.length;
//                 const isNext = index === (activeIndex + 1) % TOYS.length;

//                 // Show only active, previous and next items on mobile
//                 if (!isActive && !isPrev && !isNext) return null;

//                 // // Mobile-specific positioning
//                 let xPosition = 0;
//                 let scale = 1;
//                 let opacity = 1;
//                 let zIndex = 10;

//                 if (isActive) {
//                   xPosition = 0;
//                   scale = 1;
//                   opacity = 1;
//                   zIndex = 30;
//                 } else if (isPrev) {
//                   xPosition = -11; // Position on left
//                   scale = 0.7;
//                   opacity = 0.6;
//                   zIndex = 20;
//                 } else if (isNext) {
//                   xPosition = 65; // Position on right
//                   scale = 0.7;
//                   opacity = 0.6;
//                   zIndex = 20;
//                 }

//                 return (
//                   <motion.div
//                     key={toy.id}
//                     className="absolute top-[38%] left-[20%] flex items-center justify-center"
//                     initial={false}
//                     animate={{
//                       x: xPosition,
//                       y: "-50%",
//                       scale: scale,
//                       opacity: opacity,
//                       zIndex: zIndex,
//                     }}
//                     transition={{ duration: 0.4 }}
//                     style={{ touchAction: "pan-y" }}
//                   >
//                     <img
//                       src={toy.image}
//                       alt={toy.name}
//                       className="object-contain max-h-[300px]"
//                       style={{
//                         maxWidth: isActive ? "170px" : "120px",
//                         height: "auto",
//                       }}
//                     />
//                   </motion.div>
//                 );
//               })}
//             </div>
//           </div>

//           {/* Desktop carousel - unchanged */}
//           <div className="hidden md:block absolute inset-0">
//             {TOYS.map((toy, index) => {
//               // Calculate position based on relation to active index
//               const isActive = index === activeIndex;
//               const isPrev =
//                 index === activeIndex - 1 ||
//                 (activeIndex === 0 && index === TOYS.length - 1);
//               const isNext =
//                 index === activeIndex + 1 ||
//                 (activeIndex === TOYS.length - 1 && index === 0);

//               return (
//                 <motion.div
//                   key={toy.id}
//                   className="absolute top-1/2 w-full h-full flex items-center justify-center"
//                   initial={false}
//                   animate={{
//                     x: isActive ? 0 : isPrev ? -90 : isNext ? 90 : 0,
//                     y: "-50%",
//                     zIndex: isActive ? 30 : 10,
//                     opacity: isActive ? 1 : 0.5,
//                     scale: isActive ? 1 : 0.8,
//                   }}
//                   transition={{ duration: 0.5 }}
//                 >
//                   <img
//                     src={toy.image}
//                     alt={toy.name}
//                     className="max-h-full max-w-full object-contain drop-shadow-lg"
//                   />
//                 </motion.div>
//               );
//             })}
//           </div>

//           {/* Optional: Add touch swipe support for mobile */}
//           {/* <div
//             className="absolute inset-0 md:hidden z-40 flex"
//             style={{ touchAction: "pan-x" }}
//             onTouchStart={handleTouchStart}
//           /> */}
//         </div>
//       </div>
//     </div>
//   );
// };

// export default PlushToysCarousel;

"use client";

import React, { useState, useEffect, useRef } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useRouter } from "next/navigation";
import apiServiceWrapper from "@/lib/services/apiService";
import apiRoutes from "@/lib/constant";

const PlushToysSlider = () => {
  const [currentSlide, setCurrentSlide] = useState(1); // Start at 1 (first real slide)
  const [slides, setSlides] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [isPaused, setIsPaused] = useState(false); // For pausing auto-advance
  const router = useRouter();

  // Touch/swipe handling
  const touchStartX = useRef(0);
  const touchEndX = useRef(0);

  // Fetch slider data from API
  useEffect(() => {
    const fetchSliders = async () => {
      try {
        setLoading(true);
        const response = await apiServiceWrapper.get(apiRoutes.SLIDERS);

        // Check if the API call was successful
        if (!response.error && response.data && response.data.length > 0) {
          // Find the home slider
          const homeSlider = response.data.find(
            (slider) => slider.key === "home"
          );
          if (homeSlider && homeSlider.items && homeSlider.items.length > 0) {
            // Transform API data to match our component structure
            const transformedSlides = homeSlider.items.map((item) => ({
              id: item.id,
              highlight_text: item.highlight_text,
              title: item.title,
              subtitle: item.subtitle,
              image: item.image,
              mobileImage: item.mobile_image,
              buttonText: item.button_text || "Shop Now",
              buttonLink: item.link || "/",
            }));
            setSlides(transformedSlides);
          }
        } else {
          console.log(
            "API response error or no data:",
            response.message || "Unknown error"
          );
        }
      } catch (err) {
        console.error("Error fetching sliders:", err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchSliders();
  }, []);

  // Reset currentSlide when slides change to prevent blank screen
  useEffect(() => {
    if (slides.length > 0) {
      // Start from the first slide when slides are loaded
      if (currentSlide === 0 || currentSlide > slides.length) {
        setCurrentSlide(1);
        setIsTransitioning(false);
      }
    }
  }, [slides.length]);

  // Debug: Log current state (remove in production)
  useEffect(() => {
    console.log("Carousel State:", {
      currentSlide,
      slidesLength: slides.length,
      isTransitioning,
    });
  }, [currentSlide, slides.length, isTransitioning]);

  // Auto-advance slides
  useEffect(() => {
    if (slides.length === 0 || isPaused) return;

    const interval = setInterval(() => {
      if (!isTransitioning) {
        setIsTransitioning(true);
        setCurrentSlide((prev) => {
          // Simple loop: go to next slide or back to first
          return prev >= slides.length ? 1 : prev + 1;
        });
      }
    }, 5000); // 5 seconds for better visibility

    return () => clearInterval(interval);
  }, [slides.length, isPaused, isTransitioning]);

  // Handle transition end
  const handleTransitionEnd = () => {
    // Simply set transitioning to false
    setIsTransitioning(false);
  };

  const nextSlide = () => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrentSlide((prev) => (prev >= slides.length ? 1 : prev + 1));
    // Pause auto-advance temporarily when user manually navigates
    setIsPaused(true);
    setTimeout(() => setIsPaused(false), 6000); // Resume after 6 seconds
  };

  const prevSlide = () => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrentSlide((prev) => (prev <= 1 ? slides.length : prev - 1));
    // Pause auto-advance temporarily when user manually navigates
    setIsPaused(true);
    setTimeout(() => setIsPaused(false), 6000); // Resume after 6 seconds
  };

  const goToSlide = (index) => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrentSlide(index + 1); // index is 0-based, currentSlide is 1-based
    // Pause auto-advance temporarily when user manually navigates
    setIsPaused(true);
    setTimeout(() => setIsPaused(false), 6000); // Resume after 6 seconds
  };

  // Touch/swipe handlers
  const handleTouchStart = (e) => {
    touchStartX.current = e.touches[0].clientX;
  };

  const handleTouchMove = (e) => {
    touchEndX.current = e.touches[0].clientX;
  };

  const handleTouchEnd = () => {
    if (!touchStartX.current || !touchEndX.current) return;

    const distance = touchStartX.current - touchEndX.current;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      nextSlide();
    } else if (isRightSwipe) {
      prevSlide();
    }

    // Reset touch positions
    touchStartX.current = 0;
    touchEndX.current = 0;
  };

  // Show loading state
  if (loading) {
    return (
      <div className="relative w-full h-[300px] sm:h-[350px] md:h-[400px] lg:h-[450px] bg-custom-gradient overflow-hidden flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4"></div>
        </div>
      </div>
    );
  }

  // Show error state or fallback if no slides
  if (error || slides.length === 0) {
    return (
      <div className="relative w-full h-[300px] sm:h-[350px] md:h-[400px] lg:h-[450px] bg-custom-gradient overflow-hidden flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mb-4">Unable to load slider content</p>
          <button
            onClick={() => window.location.reload()}
            className="button_slide bg-white text-primary py-2 px-6 rounded-full font-medium"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Safety check: if currentSlide is out of bounds, reset it
  if (currentSlide < 1 || currentSlide > slides.length) {
    setTimeout(() => {
      setCurrentSlide(1);
      setIsTransitioning(false);
    }, 0);
    return (
      <div className="relative w-full h-[300px] sm:h-[350px] md:h-[400px] lg:h-[450px] bg-custom-gradient overflow-hidden flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Initializing...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-[300px] sm:h-[350px] md:h-[400px] lg:h-[450px] bg-custom-gradient overflow-hidden">
      {/* Cloud decorations */}
      <div className="absolute bottom-0 left-0 w-full">
        <svg
          viewBox="0 0 1440 100"
          fill="white"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M0,70 C150,110 350,30 500,70 C650,110 800,30 950,70 C1100,110 1300,30 1440,70 L1440,100 L0,100 Z" />
        </svg>
      </div>

      {/* Slider Container with Animation */}
      <div className="relative w-full h-full overflow-hidden">
        <div
          className={`flex h-full ${isTransitioning ? "transition-transform duration-1000 ease-in-out" : ""}`}
          style={{
            transform: `translateX(-${(currentSlide - 1) * 100}%)`, // Adjust for 1-based indexing
            minWidth: `${slides.length * 100}%`,
          }}
          onTransitionEnd={handleTransitionEnd}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          {slides.map((slide, index) => (
            <div key={slide.id} className="w-full flex-shrink-0 h-full">
              {/* Main content */}
              <div className="container mx-auto px-3 h-full flex items-center relative z-10">
                <div className="w-full max-w-6xl mx-auto">
                  {/* Mobile Layout - Horizontal like the reference image */}
                  <div className="md:hidden">
                    {slide.highlight_text && (
                      <p className="text-xs mb-1 font-bold text-teal-600 uppercase tracking-wide leading-tight">
                        {slide.highlight_text}
                      </p>
                    )}
                    <div className="grid grid-cols-5 gap-3 items-center">
                      {/* Left content - Takes 3 columns */}
                      <div className="col-span-3 space-y-2">
                        {/* Title - Large and prominent */}
                        <h1 className="text-lg font-black text-gray-900 leading-tight font-integralCF">
                          {slide.title}
                        </h1>

                        {/* Subtitle - Compact */}
                        <h2 className="text-xs font-medium text-gray-600 leading-snug line-clamp-2">
                          {slide.subtitle}
                        </h2>
                      </div>

                      {/* Right content - Image takes 2 columns */}
                      <div className="col-span-2 flex justify-center items-center">
                        <img
                          src={slide.mobileImage || slide.image}
                          alt="Promotional item"
                          className="max-h-[120px] w-auto object-contain drop-shadow-lg"
                        />
                      </div>
                    </div>
                    <div className="pt-1 mt-1 text-center">
                      <button
                        onClick={() => router.push(slide.buttonLink)}
                        className="button_slide bg-white text-primary py-1.5 px-4 rounded-full font-semibold transition-colors duration-300 text-xs shadow-md hover:shadow-lg"
                      >
                        {slide.buttonText}
                      </button>
                    </div>
                  </div>

                  {/* Desktop Layout - Side by side */}
                  <div className="hidden md:grid grid-cols-2 gap-8 items-center h-full">
                    {/* Left content */}
                    <div className="space-y-4 text-left">
                      {/* Highlight Text */}
                      {slide.highlight_text && (
                        <p className="text-sm font-semibold text-teal-600 uppercase tracking-wide">
                          {slide.highlight_text}
                        </p>
                      )}

                      {/* Title */}
                      <h1 className="text-2xl sm:text-3xl font-extrabold text-gray-900 leading-snug font-integralCF">
                        {slide.title}
                      </h1>

                      {/* Subtitle */}
                      <h2 className="text-base sm:text-lg md:text-xl font-medium text-gray-700">
                        {slide.subtitle}
                      </h2>

                      {/* Button */}
                      <div>
                        <button
                          onClick={() => router.push(slide.buttonLink)}
                          className="button_slide bg-white text-primary py-2 px-6 md:py-3 md:px-8 rounded-full font-medium transition-colors duration-300"
                        >
                          {slide.buttonText}
                        </button>
                      </div>
                    </div>

                    {/* Right content - Image */}
                    <div className="flex justify-center items-center">
                      <img
                        src={slide.image}
                        alt="Promotional item"
                        className="max-h-[350px] w-auto object-contain drop-shadow-2xl"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Navigation arrows */}
      {/* <button
        onClick={prevSlide}
        className="absolute left-2 md:left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 p-1.5 md:p-2 rounded-full shadow-lg transition-all duration-200 hover:scale-110 z-20"
        aria-label="Previous slide"
      >
        <ChevronLeft size={20} className="md:w-6 md:h-6" />
      </button>

      <button
        onClick={nextSlide}
        className="absolute right-2 md:right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 p-1.5 md:p-2 rounded-full shadow-lg transition-all duration-200 hover:scale-110 z-20"
        aria-label="Next slide"
      >
        <ChevronRight size={20} className="md:w-6 md:h-6" />
      </button> */}

      {/* Dots indicator */}
      {/* <div className="absolute bottom-4 md:bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2 z-20">
        {slides.map((_, index) => {
          // Calculate which slide is currently active (accounting for the cloned slides)
          const isActive =
            index ===
            (currentSlide === 0
              ? slides.length - 1
              : currentSlide === slides.length + 1
                ? 0
                : currentSlide - 1);

          return (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-2.5 h-2.5 md:w-3 md:h-3 rounded-full transition-all duration-200 ${
                isActive
                  ? "bg-teal-600 scale-125"
                  : "bg-white/60 hover:bg-white/80"
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          );
        })}
      </div> */}
    </div>
  );
};

export default PlushToysSlider;
