"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";

import { Plus, Minus, Star } from "lucide-react";
import {
  addToCart,
  addToCartAsync,
  removeCartItem,
  removeCartItemAsync,
} from "@/lib/features/carts/cartsSlice";
import {
  addToWishlistApi,
  removeFromWishlistApi,
} from "@/lib/features/wishlist/wishlistSlice";
import { useAppDispatch, useAppSelector } from "@/lib/hooks/redux";
import toast from "react-hot-toast";
import { getAuthToken } from "@/lib/utils";

const ProductCard = ({ data }) => {
  const dispatch = useAppDispatch();
  const token = getAuthToken(); // Get the token from local storage
  const [isInCart, setIsInCart] = useState(false);
  const [isInWishlist, setIsInWishlist] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const [isHovering, setIsHovering] = useState(false);
  const [selectedVariation, setSelectedVariation] = useState(
    data?.variations?.find((v) => v.is_default) || null
  );
  const [currentImage, setCurrentImage] = useState(
    data?.image_with_sizes?.origin[0] || ""
  );

  const { cart } = useAppSelector((state) => state.carts);
  const { items: wishlistItems } = useAppSelector((state) => state.wishlist);

  // Check if product is already in cart and wishlist on component mount or when they change
  useEffect(() => {
    const cartItem = cart?.items?.find((item) => item.id === data.id);
    if (cartItem) {
      setIsInCart(true);
      setQuantity(cartItem.quantity);
    } else {
      setIsInCart(false);
      setQuantity(1);
    }

    const wishlistItem = wishlistItems?.find((item) => item.id === data.id);
    setIsInWishlist(!!wishlistItem);
  }, [cart?.items, wishlistItems, data.id]);

  const toggleWishlist = () => {
    if (!token) {
      toast.dismiss();
      toast.error("Please log in to add products to your wishlist.");
      return;
    }
    if (isInWishlist) {
      dispatch(
        removeFromWishlistApi({
          id: data.id,
          attributes: [],
        })
      );
      toast.dismiss();
      toast.success("Removed from wishlist!");
      setIsInWishlist(false);
    } else {
      dispatch(
        addToWishlistApi({
          product_id: data.id,
          name: data.name,
          srcUrl: data?.image_with_sizes?.origin[0] || "",
          slug: data.slug,
          price: data.selling_price,
          original_price: data.original_price,
          attributes: [],
          discount: data.discount,
        })
      );
      toast.dismiss();
      toast.success("Added to wishlist!");
      setIsInWishlist(true);
    }
  };

  const updateQuantity = (newQuantity) => {
    if (!token) {
      toast.dismiss();
      toast.error("Please log in to add products to your cart.");
      return;
    }
    if (newQuantity < 1) return;

    const currentVariation = selectedVariation || data;
    const stockQuantity = currentVariation.quantity || data.quantity;

    if (newQuantity > stockQuantity) {
      if (stockQuantity == 0) {
        toast.dismiss();
        toast.error("Out of stock");
      } else {
        toast.dismiss();
        toast.error(`Only ${stockQuantity} items are in stock.`);
      }
      return;
    }
    toast.dismiss();
    toast.success("Added to cart!");
    setQuantity(newQuantity);

    // For updating quantity, we dispatch addToCart with the updated quantity
    dispatch(
      addToCart({
        ...data,
        variation: selectedVariation ? selectedVariation.id : null,
        quantity: newQuantity,
      })
    );
    dispatch(
      addToCartAsync({
        ...data,
        product_id: data.id,
        variation_id: selectedVariation ? selectedVariation.id : null,
        quantity: newQuantity,
        qty: newQuantity,
      })
    );
  };

  const calculateDiscountPercentage = () => {
    return Math.round(
      ((parseFloat(data.original_price) - parseFloat(data.selling_price)) /
        parseFloat(data.original_price)) *
        100
    );
  };

  const hasDiscount = parseFloat(data.original_price) > parseFloat(data.price);

  const handleVariationSelect = (variation) => {
    setSelectedVariation(variation);
    // If the variation has an image attribute with an image, update the displayed image
    const variationImage =
      variation?.image ?? data?.image_with_sizes?.origin[0];
    if (variationImage) {
      setCurrentImage(variationImage);
    } else {
      // Reset to default product image if variation has no image
      setCurrentImage(data?.image_with_sizes?.origin[0] || "");
    }
  };

  return (
    <div className="hover:scale-[95%] transition-all relative flex flex-col items-start bg-white rounded-xl border border-gray-200 overflow-hidden">
      {/* Product Image Section */}
      <Link
        href={`/shop/product/${data.slug}`}
        className="relative w-full aspect-square"
      >
        <div className="relative w-full aspect-square">
          <img
            id={`product-image-${data.id}`}
            src={currentImage}
            alt={data.name}
            className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
          />
        </div>
        <div className="absolute top-2 right-2 flex items-center px-2 py-1 rounded-full text-xs">
          {/* <Star className="w-4 h-4 mr-1" />
          {data.rating || "4.5"} */}
          <button
            onClick={(e) => {
              e.stopPropagation(); // Prevent Link click
              e.preventDefault(); // Prevent default link navigation
              toggleWishlist(); // Toggle wishlist state
            }}
            className={`absolute top-[-0.2rem] left-[-1rem] p-2 rounded-full ${
              isInWishlist ? "bg-white text-primary" : "bg-gray-100"
            }`}
          >
            <svg
              className={`w-5 h-5 ${
                isInWishlist
                  ? "fill-primary text-primary"
                  : "fill-none text-primary"
              }`}
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path
                d="M12 20L4.3314 12.0474C3.47892 11.1633 3 9.96429 3 8.71405C3 6.11055 5.03517 4 7.54569 4C8.75128 4 9.90749 4.49666 10.76 5.38071L12 6.66667L13.24 5.38071C14.0925 4.49666 15.2487 4 16.4543 4C18.9648 4 21 6.11055 21 8.71405C21 9.96429 20.5211 11.1633 19.6686 12.0474L15.8343 16.0237"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>
      </Link>

      {/* Product Info */}
      <div className="p-4 w-full">
        <Link href={`/shop/product/${data.slug}`}>
          <h3 className="text-start text-gray-800 font-medium text-sm line-clamp-2">
            {data.name}
          </h3>
        </Link>

        {/* Reviews Section */}
        {(data.reviews_avg > 0 || data.reviews_count > 0) && (
          <div className="mt-2">
            {/* Desktop layout - all in one row */}
            <div className="hidden sm:flex items-center space-x-1">
              <div className="flex items-center">
                {[...Array(5)].map((_, index) => (
                  <Star
                    key={index}
                    className={`w-3 h-3 ${
                      index < Math.floor(data.reviews_avg || 0)
                        ? "text-yellow-400 fill-yellow-400"
                        : index < (data.reviews_avg || 0)
                          ? "text-yellow-400 fill-yellow-400"
                          : "text-gray-300"
                    }`}
                  />
                ))}
              </div>
              <span className="text-xs text-gray-600">
                {data.reviews_avg ? data.reviews_avg.toFixed(1) : "0.0"}
              </span>
              <span className="text-xs text-gray-400">
                ({data.reviews_count || 0} review
                {data.reviews_count !== 1 ? "s" : ""})
              </span>
            </div>

            {/* Mobile layout - stars and rating on first row, count on second row */}
            <div className="sm:hidden">
              <div className="flex items-center space-x-1">
                <div className="flex items-center">
                  {[...Array(5)].map((_, index) => (
                    <Star
                      key={index}
                      className={`w-3 h-3 ${
                        index < Math.floor(data.reviews_avg || 0)
                          ? "text-yellow-400 fill-yellow-400"
                          : index < (data.reviews_avg || 0)
                            ? "text-yellow-400 fill-yellow-400"
                            : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>
                <span className="text-xs text-gray-600">
                  {data.reviews_avg ? data.reviews_avg.toFixed(1) : "0.0"}
                </span>
              </div>
              <div className="mt-1">
                <span className="text-xs text-gray-400">
                  ({data.reviews_count || 0} review
                  {data.reviews_count !== 1 ? "s" : ""})
                </span>
              </div>
            </div>
          </div>
        )}

        <div className="flex items-center space-x-2 mt-2">
          <span className="text-base sm:text-lg font-semibold text-primary">
            {selectedVariation
              ? selectedVariation.price_formatted
              : data.price_formatted}
          </span>
          {(selectedVariation
            ? parseFloat(selectedVariation.original_price) >
              parseFloat(selectedVariation.price)
            : hasDiscount) && (
            <span className="text-xs sm:text-sm line-through text-gray-400">
              {selectedVariation
                ? selectedVariation.original_price_formatted
                : data.original_price_formatted}
            </span>
          )}
          {/* {hasDiscount && (
            <span className="text-xs sm:text-sm text-green-600 font-medium">
              {calculateDiscountPercentage()}% OFF
            </span>
          )} */}
        </div>
      </div>

      {data?.variations && data.variations.length > 0 && (
        <div className="px-4 pb-2">
          <div className="flex items-center gap-2">
            {data.variations.map((variation) => (
              <button
                key={variation.id}
                onClick={() => handleVariationSelect(variation)}
                className={`w-6 h-6 rounded-full border-2 transition-all ${
                  selectedVariation?.id === variation.id
                    ? "border-primary shadow-sm scale-110"
                    : "border-gray-300"
                }`}
                title={variation.attributes[0]?.title}
              >
                <span
                  className="block w-full h-full rounded-full"
                  style={{
                    backgroundColor: variation.attributes[0]?.color || "#ccc",
                  }}
                />
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Add to Cart */}
      <div className="px-4 pb-4 mt-auto flex flex-col sm:flex-row items-center justify-center w-full space-y-2 sm:space-y-0 sm:space-x-4">
        {!isInCart ? (
          <button
            onClick={() => {
              updateQuantity(1);
            }}
            className="bg-primary text-white md:px-4 md:py-2 px-3 py-1.5 w-full sm:w-auto rounded-lg text-sm sm:text-base hover:bg-secondary-10-opacity hover:text-primary hover:border hover:border-primary"
          >
            Add to Cart
          </button>
        ) : (
          <div className="flex items-center justify-center space-x-2 w-full sm:w-auto">
            <button
              onClick={() => {
                if (quantity > 1) {
                  updateQuantity(quantity - 1);
                } else {
                  toast.success("Removed from cart!");
                  dispatch(removeCartItemAsync({ id: data.id }));
                  dispatch(removeCartItem({ id: data.id }));
                  setIsInCart(false);
                }
              }}
              className="p-2 bg-gray-100 rounded-full hover:bg-gray-200"
            >
              {quantity === 1 ? (
                <img src="/icons/delete.svg" alt="delete" className="w-5 h-5" />
              ) : (
                <Minus className="w-5 h-5" />
              )}
            </button>
            <span className="text-sm sm:text-base font-medium">{quantity}</span>
            <button
              onClick={() => updateQuantity(quantity + 1)}
              className="p-2 bg-gray-100 rounded-full hover:bg-gray-200"
            >
              <Plus className="w-5 h-5" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductCard;
