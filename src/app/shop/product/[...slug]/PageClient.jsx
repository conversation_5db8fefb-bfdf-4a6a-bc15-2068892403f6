"use client";

import React, { useEffect, useState } from "react";
import {
  Star,
  Heart,
  Plus,
  Minus,
  Truck,
  Headphones,
  ShoppingCart,
} from "lucide-react";

import Image from "next/image";
import { storeRecentlyViewedProduct } from "@/lib/features/products/productsSlice";
import { useDispatch } from "react-redux";
import toast from "react-hot-toast";
import { addToCart, addToCartAsync } from "@/lib/features/carts/cartsSlice";
import { useAppSelector } from "@/lib/hooks/redux";
import Link from "next/link";
import {
  addToWishlistApi,
  removeFromWishlistApi,
} from "@/lib/features/wishlist/wishlistSlice";
import VariationSelector from "@/components/product-page/VariationSelector";
import { getAuthToken } from "@/lib/utils";
import ProductCard from "@/components/common/ProductCard";

const ProductPageClient = ({ productData, relativeProducts }) => {
  const [selectedVariant, setSelectedVariant] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [activeTab, setActiveTab] = useState("description");
  const [selectedImage, setSelectedImage] = useState(0);
  const [isInCart, setIsInCart] = useState(false);
  const [isInWishlist, setIsInWishlist] = useState(false);
  const [selectedAttributes, setSelectedAttributes] = useState([]);
  const [currentVariation, setCurrentVariation] = useState(null);
  const [reviews, setReviews] = useState([]);
  const [reviewStats, setReviewStats] = useState({
    average: 0,
    count: 0,
    distribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 },
  });

  const { cart } = useAppSelector((state) => state.carts);
  const { items: wishlistItems } = useAppSelector((state) => state.wishlist);
  const token = getAuthToken();

  useEffect(() => {
    if (productData?.selected_attributes) {
      setSelectedAttributes(productData.selected_attributes);
    }

    if (productData?.default_product_variation) {
      setCurrentVariation(productData.default_product_variation);
    }

    // Set reviews data from productData
    if (productData?.reviews) {
      const reviewsData = productData.reviews.data || [];
      setReviews(reviewsData);

      // Calculate dynamic distribution from reviews
      const distribution = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 };
      reviewsData.forEach((review) => {
        if (review.star >= 1 && review.star <= 5) {
          distribution[review.star]++;
        }
      });

      // Calculate average from reviews if not provided
      const totalStars = reviewsData.reduce(
        (sum, review) => sum + (review.star || 0),
        0
      );
      const calculatedAverage =
        reviewsData.length > 0 ? totalStars / reviewsData.length : 0;

      setReviewStats({
        average: productData.reviews_avg || calculatedAverage,
        count: productData.reviews_count || reviewsData.length,
        distribution: distribution,
      });
    }
  }, [productData]);

  // Get product images - fallback to placeholder if no images
  const productImages = productData?.images?.length
    ? productData.images.map((img) => img)
    : [];

  // Get product variants (colors/options)
  const variants = productData?.variants || productData?.colors || [];

  // Calculate discounted price
  const originalPrice = productData?.original_price_formatted || 0;
  const discountedPrice = productData?.price_formatted || originalPrice;
  const discountPercentage =
    originalPrice > discountedPrice
      ? Math.round(((originalPrice - discountedPrice) / originalPrice) * 100)
      : 0;

  useEffect(() => {
    const cartItem = cart?.items?.find((item) => item.id === productData.id);
    if (cartItem) {
      setIsInCart(true);
      setQuantity(cartItem.quantity);
    } else {
      setIsInCart(false);
      setQuantity(1);
    }

    const wishlistItem = wishlistItems?.find(
      (item) => item.id === productData.id
    );
    setIsInWishlist(!!wishlistItem);
  }, [cart?.items]);

  const renderStars = (rating) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`w-4 h-4 ${
              star <= fullStars
                ? "fill-yellow-400 text-yellow-400"
                : star === fullStars + 1 && hasHalfStar
                  ? "fill-yellow-400/50 text-yellow-400"
                  : "text-gray-300"
            }`}
          />
        ))}
      </div>
    );
  };

  const handleQuantityChange = (type) => {
    if (!token) {
      toast.dismiss();
      toast.error("Please log in to add products to your cart.");
      return;
    }
    if (type === "increment") {
      if (quantity >= productData.quantity) {
        toast.dismiss();
        toast.error("You can't add more quantity.");
        return;
      }
      setQuantity((prev) => prev + 1);
    } else if (type === "decrement" && quantity > 1) {
      setQuantity((prev) => prev - 1);
    }
  };

  const toggleWishlist = () => {
    if (!token) {
      toast.dismiss();
      toast.error("Please log in to add products to your wishlist.");
      return;
    }
    if (isInWishlist) {
      dispatch(
        removeFromWishlistApi({
          id: productData.id,
          attributes: [],
        })
      );
      toast.dismiss();
      toast.success("Removed from wishlist!");
      setIsInWishlist(false);
    } else {
      dispatch(
        addToWishlistApi({
          product_id: productData.id,
          name: productData.name,
          srcUrl: productData?.image_with_sizes?.origin[0] || "",
          slug: productData.slug,
          price: productData.selling_price,
          original_price: productData.original_price,
          attributes: [],
          discount: productData.discount,
        })
      );
      toast.dismiss();
      toast.success("Added to wishlist!");
      setIsInWishlist(true);
    }
  };

  const handleSelectVariation = async (attributeSetId, attributeId) => {
    // Create a new array of selected attributes
    const newSelectedAttributes = [...selectedAttributes];

    // Find if we already have a selection for this attribute set
    const existingIndex = newSelectedAttributes.findIndex(
      (attr) => attr.attribute_set.id === attributeSetId
    );

    // Find the attribute set and attribute from the available options
    const attributeSet = productData.attribute_sets.find(
      (set) => set.id === attributeSetId
    );
    const attribute = attributeSet?.attributes.find(
      (attr) => attr.id === attributeId
    );

    if (!attribute) return;

    // If we already have a selection for this attribute set, replace it
    if (existingIndex >= 0) {
      newSelectedAttributes[existingIndex] = {
        ...attribute,
        attribute_set: {
          id: attributeSet.id,
          title: attributeSet.title,
          slug: attributeSet.slug,
          order: attributeSet.order,
          display_layout: attributeSet.display_layout,
        },
      };
    } else {
      // Otherwise add a new selection
      newSelectedAttributes.push({
        ...attribute,
        attribute_set: {
          id: attributeSet.id,
          title: attributeSet.title,
          slug: attributeSet.slug,
          order: attributeSet.order,
          display_layout: attributeSet.display_layout,
        },
      });
    }

    setSelectedAttributes(newSelectedAttributes);
  };

  const handleAddToCart = () => {
    if (!token) {
      toast.dismiss();
      toast.error("Please log in to add products to your cart.");
      return;
    }
    if (quantity >= productData.quantity) {
      toast.dismiss();
      toast.error("You can't add more quantity.");
      return;
    }
    toast.dismiss();
    toast.success("Added to cart!");
    const productToAdd = {
      ...productData,
      quantity,
      // Include the selected variation data
      variation_id: currentVariation?.id,
      attributes: selectedAttributes.map((attr) => ({
        id: attr.id,
        name: attr.attribute_set.title,
        value: attr.title,
      })),
    };

    dispatch(addToCart(productToAdd));
    dispatch(
      addToCartAsync({
        ...productToAdd,
        product_id: productData.id,
        qty: quantity,
      })
    );
  };

  const dispatch = useDispatch();
  useEffect(() => {
    if (productData?.id) {
      dispatch(storeRecentlyViewedProduct(productData?.id));
    }
  }, [productData, dispatch]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white px-4 py-3 border-b">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Link href={"/"}>Home</Link>
            <span>›</span>
            <Link href={"/shop"}>Shop</Link>
            <span>›</span>
            <span className="text-gray-900">{productData?.name}</span>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Product   image_urls?[];
           */}
          <div className="space-y-4">
            {/* Main Image */}
            <div className="bg-gradient-to-br from-orange-100 to-pink-100 rounded-2xl overflow-hidden shadow-lg flex items-center justify-center min-h-[400px]">
              {productImages[selectedImage] ? (
                <img
                  src={productImages[selectedImage]}
                  alt={productData?.name || "Product Image"}
                  className="max-w-full max-h-full object-contain"
                  style={{
                    width: "auto",
                    height: "auto",
                    maxWidth: "100%",
                    maxHeight: "600px",
                  }}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <Heart className="w-16 h-16 text-pink-400" />
                </div>
              )}
            </div>

            {/* Thumbnail Images */}
            {productImages.length > 1 && (
              <div className="flex gap-3 overflow-x-scroll scrollbar-none scrollbar-thumb-gray-400 scrollbar-track-gray-200">
                {productImages.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImage(index)}
                    className={`w-24 h-24 rounded-xl overflow-hidden border-2 transition-all flex-shrink-0 ${
                      selectedImage === index
                        ? "border-primary shadow-md"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                  >
                    {image ? (
                      <img
                        src={image}
                        alt={`${productData?.name} ${index + 1}`}
                        className="w-full h-full object-contain"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-pink-100 to-blue-100 flex items-center justify-center">
                        <Heart className="w-6 h-6 text-pink-400" />
                      </div>
                    )}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Details */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {productData?.name?.toUpperCase()}
              </h1>

              <div className="flex items-center space-x-4 mb-4">
                <div className="flex items-center space-x-2">
                  <span className="text-2xl font-bold text-primary">
                    {currentVariation?.price_formatted || discountedPrice}
                  </span>
                  {(currentVariation?.original_price >
                    currentVariation?.price ||
                    discountPercentage > 0) && (
                    <>
                      <span className="text-lg text-gray-500 line-through">
                        {currentVariation?.original_price_formatted ||
                          originalPrice}
                      </span>
                      {discountPercentage > 0 && (
                        <span className="bg-pink-500 text-white px-2 py-1 rounded-full text-sm font-medium">
                          -{discountPercentage}%
                        </span>
                      )}
                    </>
                  )}
                </div>
              </div>

              {(reviewStats.average > 0 || productData?.rating) && (
                <div
                  className="flex items-center space-x-2 mb-2 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors"
                  onClick={() => {
                    setActiveTab("reviews");
                    // Scroll to reviews section
                    setTimeout(() => {
                      const reviewsSection =
                        document.getElementById("reviews-section");
                      if (reviewsSection) {
                        reviewsSection.scrollIntoView({ behavior: "smooth" });
                      }
                    }, 100);
                  }}
                >
                  {renderStars(reviewStats.average || productData.rating)}
                  <span className="text-sm font-medium text-gray-900">
                    {(reviewStats.average || productData.rating || 0).toFixed(
                      1
                    )}
                  </span>
                  <span className="text-sm text-gray-600">
                    ({reviewStats.count || productData?.review_count || 0})
                  </span>
                </div>
              )}

              {productData?.description ? (
                <div
                  dangerouslySetInnerHTML={{ __html: productData?.description }}
                  className="product-description text-gray-600 mb-6"
                ></div>
              ) : null}
            </div>

            {/* Variants/Color Selection */}
            {productData?.attribute_sets?.length > 0 && (
              <VariationSelector
                attributeSets={productData.attribute_sets}
                selectedAttributes={selectedAttributes}
                unavailableAttributeIds={productData.unavailable_attribute_ids}
                onSelectVariation={handleSelectVariation}
              />
            )}

            {/* Quantity */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                Quantity
              </h3>
              <div className="flex items-center space-x-4 mb-4">
                <div className="flex items-center border border-gray-300 rounded-lg">
                  <button
                    onClick={() => handleQuantityChange("decrement")}
                    className="p-2 hover:bg-gray-100 transition-colors disabled:opacity-50"
                    disabled={quantity <= 1}
                  >
                    <Minus className="w-4 h-4" />
                  </button>
                  <span className="px-4 py-2 border-x border-gray-300 min-w-[3rem] text-center">
                    {quantity}
                  </span>
                  <button
                    onClick={() => handleQuantityChange("increment")}
                    className="p-2 hover:bg-gray-100 transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>

            {/* Add to Cart */}
            <div className=" mt-5">
              <div className="flex space-x-4">
                <button
                  onClick={handleAddToCart}
                  className="flex-1 mb-5 bg-primary hover:bg-secondary text-white font-semibold py-3 px-6 rounded-full transition-colors flex items-center justify-center space-x-2"
                >
                  <ShoppingCart className="w-5 h-5" />
                  <span>Add to Cart</span>
                </button>
                <button
                  onClick={() => toggleWishlist()}
                  className={`p-3 border mb-5 border-gray-300 rounded-full transition-colors ${
                    isInWishlist
                      ? "bg-pink-50 border-pink-300"
                      : "hover:bg-gray-50"
                  }`}
                >
                  <Heart
                    className={`w-6 h-6 ${
                      isInWishlist
                        ? "fill-pink-500 text-pink-500"
                        : "text-gray-600"
                    }`}
                  />
                </button>
              </div>
            </div>

            {/* Features */}
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-3 p-4 bg-white rounded-xl border border-gray-200">
                <div className="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center">
                  <Truck className="w-5 h-5 text-pink-600" />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Free Delivery</p>
                  <p className="text-sm text-gray-600">Orders over ₹999</p>
                </div>
              </div>

              {/* <div className="flex items-center space-x-3 p-4 bg-white rounded-xl border border-gray-200">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <Shield className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">
                    6 Months Warranty
                  </p>
                  <p className="text-sm text-gray-600">100% Genuine</p>
                </div>
              </div>

              <div className="flex items-center space-x-3 p-4 bg-white rounded-xl border border-gray-200">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <RotateCcw className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Easy Returns</p>
                  <p className="text-sm text-gray-600">10-day return policy</p>
                </div>
              </div> */}

              <div className="flex items-center space-x-3 p-4 bg-white rounded-xl border border-gray-200">
                <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                  <Headphones className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">24/7 Support</p>
                  <p className="text-sm text-gray-600">Dedicated support</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Product Information Tabs */}
        <div
          id="reviews-section"
          className="mt-16 bg-white rounded-2xl border border-gray-200 overflow-hidden"
        >
          {/* Tab Headers */}
          <div className="flex border-b border-gray-200">
            {["description", "reviews"].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-8 py-4 font-medium capitalize transition-colors ${
                  activeTab === tab
                    ? "text-white border-b-2 border-secondary bg-primary"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                {tab === "reviews" ? `Reviews (${reviewStats.count})` : tab}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div className="p-8">
            {activeTab === "description" && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    Product Description
                  </h3>
                  <div
                    className="text-gray-600 product-description leading-relaxed prose max-w-none"
                    dangerouslySetInnerHTML={{
                      __html:
                        productData?.content || "No description available.",
                    }}
                  />
                </div>

                {productData?.specifications && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-3">
                        Specifications
                      </h4>
                      <div className="space-y-2 text-sm">
                        {Object.entries(productData.specifications).map(
                          ([key, value]) => (
                            <div key={key} className="flex justify-between">
                              <span className="text-gray-600 capitalize">
                                {key.replace("_", " ")}:
                              </span>
                              <span className="text-gray-900">{value}</span>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === "features" && (
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-gray-900">
                  Key Features
                </h3>
                {productData?.features ? (
                  <ul className="space-y-3">
                    {productData.features.map((feature, index) => (
                      <li key={index} className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-pink-500 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-gray-600">
                    No features information available.
                  </p>
                )}
              </div>
            )}

            {activeTab === "reviews" && (
              <div className="space-y-6">
                {/* Reviews Header */}
                <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">
                      Customer Reviews
                    </h3>
                    {reviewStats.count > 0 && (
                      <div className="flex flex-col sm:flex-row sm:items-center gap-6">
                        {/* Overall Rating */}
                        <div className="flex items-center space-x-3">
                          <div className="text-center">
                            <div className="text-4xl font-bold text-gray-900 mb-1">
                              {reviewStats.average.toFixed(1)}
                            </div>
                            <div className="flex items-center justify-center space-x-1 mb-1">
                              {renderStars(reviewStats.average)}
                            </div>
                            <div className="text-sm text-gray-600">
                              {reviewStats.count} review
                              {reviewStats.count !== 1 ? "s" : ""}
                            </div>
                          </div>
                        </div>

                        {/* Rating Breakdown */}
                        {reviewStats.distribution && (
                          <div className="flex-1 max-w-sm">
                            {[5, 4, 3, 2, 1].map((rating) => {
                              const count =
                                reviewStats.distribution[rating] || 0;
                              const percentage =
                                reviewStats.count > 0
                                  ? (count / reviewStats.count) * 100
                                  : 0;

                              return (
                                <div
                                  key={rating}
                                  className="flex items-center space-x-2 mb-1"
                                >
                                  <span className="text-sm text-gray-600 w-8">
                                    {rating}
                                  </span>
                                  <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                                    <div
                                      className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                                      style={{ width: `${percentage}%` }}
                                    ></div>
                                  </div>
                                  <span className="text-sm text-gray-600 w-8">
                                    {count}
                                  </span>
                                </div>
                              );
                            })}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                {/* Reviews List */}
                {reviews.length > 0 ? (
                  <div className="space-y-6">
                    {reviews.map((review, index) => (
                      <div
                        key={review.id}
                        className={`border-b border-gray-200 pb-4 ${index != 1 ? "pt-4" : "0"}`}
                      >
                        <div className="flex items-start space-x-4">
                          {/* User Avatar */}
                          <div className="flex-shrink-0">
                            {review.user_avatar ? (
                              <img
                                src={review.user_avatar}
                                alt={review.user_name}
                                className="w-12 h-12 rounded-full object-cover"
                              />
                            ) : (
                              <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                                <span className="text-gray-600 font-medium text-lg">
                                  {review.user_name?.charAt(0)?.toUpperCase()}
                                </span>
                              </div>
                            )}
                          </div>

                          {/* Review Content */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-2">
                              <div>
                                <h4 className="font-medium text-gray-900">
                                  {review.user_name}
                                </h4>
                                <div className="flex items-center space-x-2 mt-1">
                                  {renderStars(review.star)}
                                  <span className="text-sm text-gray-500">
                                    {review.created_at}
                                  </span>
                                </div>
                              </div>
                            </div>

                            {/* Review Comment */}
                            <p className="text-gray-700 mb-3">
                              {review.comment}
                            </p>

                            {/* Review Images */}
                            {review.images && review.images.length > 0 && (
                              <div className="flex space-x-2 mt-3">
                                {review.images.map((image, index) => (
                                  <img
                                    key={index}
                                    src={image}
                                    alt={`Review image ${index + 1}`}
                                    className="w-16 h-16 object-cover rounded-lg border border-gray-200"
                                  />
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                      <Star className="w-8 h-8 text-gray-400" />
                    </div>
                    <h4 className="text-lg font-medium text-gray-900 mb-2">
                      No reviews yet
                    </h4>
                    <p className="text-gray-600">
                      Be the first to review this product!
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Related Products */}
        {relativeProducts?.length > 0 && (
          <div className="mt-16">
            <h2 className="text-2xl font-bold text-gray-900 mb-8">
              You might also like
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {relativeProducts?.slice(0, 4).map((product) => (
                <ProductCard key={product.id} data={product} />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductPageClient;
